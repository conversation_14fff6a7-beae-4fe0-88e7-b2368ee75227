"""
Car Model Specific Notification Signals
Handles notifications for vehicle compatibility, parts alerts, and maintenance reminders
"""

import logging
from django.db.models.signals import post_save, pre_save, m2m_changed
from django.dispatch import receiver
from django.utils.translation import gettext as _
from django.utils import timezone
from datetime import datetime, timedelta

from .services import NotificationService

logger = logging.getLogger(__name__)

@receiver(post_save)
def check_part_vehicle_compatibility(sender, instance, created, **kwargs):
    """
    Check if work order items are compatible with the assigned vehicle
    Send notifications for incompatible parts
    """
    try:
        # Check if this is a WorkOrderMaterial
        if sender.__name__ != 'WorkOrderMaterial':
            return

        if not created:
            print("Not created, returning")
            return

        work_order = instance.work_order
        vehicle = work_order.vehicle
        item = instance.item

        print(f"Work order: {work_order}, Item: {item}, Vehicle: {vehicle}")

        if not vehicle or not item:
            print("No vehicle or item, returning")
            return
            
        # Check if item is compatible with vehicle using VehicleCompatibility
        is_compatible = item.vehicle_compatibilities.filter(
            make=vehicle.make,
            model=vehicle.model
        ).exists()

        # If year is specified, check year range
        if vehicle.year and is_compatible:
            is_compatible = item.vehicle_compatibilities.filter(
                make=vehicle.make,
                model=vehicle.model,
                year_from__lte=vehicle.year,
                year_to__gte=vehicle.year
            ).exists() or item.vehicle_compatibilities.filter(
                make=vehicle.make,
                model=vehicle.model,
                year_from__lte=vehicle.year,
                year_to__isnull=True
            ).exists()
        
        if not is_compatible:
            # Create incompatible parts notification
            # Use assigned technician or fallback to any user for testing
            recipient = work_order.assigned_technician
            if not recipient:
                # Fallback to any user in the system for testing
                from django.contrib.auth import get_user_model
                User = get_user_model()
                recipient = User.objects.first()

            if recipient:
                NotificationService.create_notification(
                    recipient=recipient,
                notification_type_code='incompatible_part_alert',
                title=_('تحذير: قطعة غير متوافقة مع المركبة'),
                message=_(
                    'القطعة "{item_name}" غير متوافقة مع {vehicle_make} {vehicle_model} {vehicle_year}. '
                    'يرجى التحقق من التوافق قبل المتابعة.'
                ).format(
                    item_name=item.name,
                    vehicle_make=vehicle.make,
                    vehicle_model=vehicle.model,
                    vehicle_year=vehicle.year or _('غير محدد')
                ),
                priority='high',
                action_required=True,
                action_url=f'/work-orders/{work_order.id}/',
                action_text=_('مراجعة أمر العمل'),
                related_object_type='work_order',
                related_object_id=str(work_order.id),
                tenant_id=work_order.tenant_id,
                metadata={
                    'work_order_id': str(work_order.id),
                    'item_id': str(item.id),
                    'vehicle_make': vehicle.make,
                    'vehicle_model': vehicle.model,
                    'vehicle_year': vehicle.year,
                    'compatibility_issue': 'part_vehicle_mismatch'
                }
            )
            
            # Also notify service center manager if different from assigned technician
            if work_order.service_center and work_order.service_center.manager:
                manager = work_order.service_center.manager
                if manager != work_order.assigned_to:
                    NotificationService.create_notification(
                        recipient=manager,
                        notification_type_code='manager_compatibility_alert',
                        title=_('تنبيه مدير: قطعة غير متوافقة'),
                        message=_(
                            'تم إضافة قطعة غير متوافقة "{item_name}" لأمر العمل #{work_order_number} '
                            'للمركبة {vehicle_make} {vehicle_model}. يتطلب مراجعة فورية.'
                        ).format(
                            item_name=item.name,
                            work_order_number=work_order.work_order_number,
                            vehicle_make=vehicle.make,
                            vehicle_model=vehicle.model
                        ),
                        priority='high',
                        action_required=True,
                        action_url=f'/work-orders/{work_order.id}/',
                        action_text=_('مراجعة أمر العمل'),
                        tenant_id=work_order.tenant_id,
                        metadata={
                            'work_order_id': str(work_order.id),
                            'item_id': str(item.id),
                            'alert_type': 'manager_compatibility_review'
                        }
                    )
                    
        logger.info(f"Checked compatibility for item {item.id} with vehicle {vehicle.id}")
        
    except Exception as e:
        logger.error(f"Error checking part-vehicle compatibility: {e}")


@receiver(post_save, sender='setup.Vehicle')
def create_vehicle_maintenance_reminders(sender, instance, created, **kwargs):
    """
    Create maintenance reminders based on vehicle make/model/year
    """
    try:
        if not created:
            return
            
        vehicle = instance
        
        # Get maintenance schedules for this vehicle type
        from inventory.models import OperationCompatibility
        
        maintenance_operations = OperationCompatibility.get_compatible_operations(vehicle)
        
        if maintenance_operations.exists():
            # Create notification for service advisor about available maintenance schedules
            NotificationService.create_notification(
                recipient=vehicle.created_by,
                notification_type_code='vehicle_maintenance_schedule',
                title=_('جدولة صيانة متاحة للمركبة الجديدة'),
                message=_(
                    'تم العثور على {count} عملية صيانة متوافقة مع {vehicle_make} {vehicle_model} {vehicle_year}. '
                    'يمكنك إنشاء جدولة صيانة وقائية للمركبة.'
                ).format(
                    count=maintenance_operations.count(),
                    vehicle_make=vehicle.make,
                    vehicle_model=vehicle.model,
                    vehicle_year=vehicle.year or _('غير محدد')
                ),
                priority='medium',
                action_required=True,
                action_url=f'/vehicles/{vehicle.id}/maintenance/',
                action_text=_('إنشاء جدولة صيانة'),
                related_object_type='vehicle',
                related_object_id=str(vehicle.id),
                tenant_id=vehicle.tenant_id,
                metadata={
                    'vehicle_id': str(vehicle.id),
                    'maintenance_count': maintenance_operations.count(),
                    'vehicle_specs': {
                        'make': vehicle.make,
                        'model': vehicle.model,
                        'year': vehicle.year
                    }
                }
            )
            
        logger.info(f"Created maintenance reminders for vehicle {vehicle.id}")
        
    except Exception as e:
        logger.error(f"Error creating vehicle maintenance reminders: {e}")


@receiver(post_save, sender='inventory.Item')
def check_new_part_vehicle_compatibility(sender, instance, created, **kwargs):
    """
    When new parts are added, check which vehicles they're compatible with
    and notify relevant service centers
    """
    try:
        if not created:
            return
            
        item = instance
        
        # Get all vehicle compatibilities for this item
        compatibilities = item.vehicle_compatibilities.all()
        
        if compatibilities.exists():
            # Group by make/model for notification
            vehicle_types = {}
            for compat in compatibilities:
                key = f"{compat.make}_{compat.model}"
                if key not in vehicle_types:
                    vehicle_types[key] = {
                        'make': compat.make,
                        'model': compat.model,
                        'year_ranges': []
                    }
                
                year_range = f"{compat.year_from}"
                if compat.year_to:
                    year_range += f"-{compat.year_to}"
                else:
                    year_range += "+"
                    
                vehicle_types[key]['year_ranges'].append(year_range)
            
            # Create notification about new compatible part
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # Notify service advisors and managers
            service_staff = User.objects.filter(
                groups__name__in=['service_advisor', 'service_center_manager'],
                userprofile__tenant_id=item.tenant_id
            ).distinct()
            
            for user in service_staff:
                vehicle_list = []
                for vtype in vehicle_types.values():
                    years = ', '.join(vtype['year_ranges'])
                    vehicle_list.append(f"{vtype['make']} {vtype['model']} ({years})")
                
                NotificationService.create_notification(
                    recipient=user,
                    notification_type_code='new_compatible_part',
                    title=_('قطعة جديدة متوافقة متاحة'),
                    message=_(
                        'تم إضافة قطعة جديدة "{item_name}" متوافقة مع: {vehicle_list}. '
                        'القطعة متاحة الآن للاستخدام في أوامر العمل.'
                    ).format(
                        item_name=item.name,
                        vehicle_list='; '.join(vehicle_list)
                    ),
                    priority='low',
                    action_required=False,
                    action_url=f'/inventory/items/{item.id}/',
                    action_text=_('عرض القطعة'),
                    related_object_type='item',
                    related_object_id=str(item.id),
                    tenant_id=item.tenant_id,
                    metadata={
                        'item_id': str(item.id),
                        'compatible_vehicles': list(vehicle_types.values()),
                        'notification_type': 'new_part_availability'
                    }
                )
                
        logger.info(f"Checked vehicle compatibility for new item {item.id}")

    except Exception as e:
        logger.error(f"Error checking new part vehicle compatibility: {e}")


@receiver(post_save, sender='inventory.Movement')
def check_low_stock_vehicle_specific(sender, instance, created, **kwargs):
    """
    Check for low stock on vehicle-specific parts and notify relevant service centers
    """
    try:
        if not created or instance.movement_type != 'out':
            return

        item = instance.item
        current_stock = item.current_stock

        # Check if stock is low (less than 5 units or below reorder level)
        reorder_level = getattr(item, 'reorder_level', 5)
        if current_stock > reorder_level:
            return

        # Get vehicle compatibilities for this item
        compatibilities = item.vehicle_compatibilities.all()

        if compatibilities.exists():
            # Group by make/model
            vehicle_types = set()
            for compat in compatibilities:
                vehicle_types.add(f"{compat.make} {compat.model}")

            # Notify warehouse and service staff
            from django.contrib.auth import get_user_model
            User = get_user_model()

            # Get warehouse and service staff
            relevant_staff = User.objects.filter(
                groups__name__in=['warehouse_manager', 'service_advisor', 'service_center_manager'],
                userprofile__tenant_id=item.tenant_id
            ).distinct()

            for user in relevant_staff:
                NotificationService.create_notification(
                    recipient=user,
                    notification_type_code='low_stock_vehicle_part',
                    title=_('نفاد مخزون قطعة خاصة بمركبة'),
                    message=_(
                        'مخزون القطعة "{item_name}" منخفض ({current_stock} وحدة متبقية). '
                        'هذه القطعة مطلوبة لـ: {vehicle_types}. يرجى إعادة الطلب فوراً.'
                    ).format(
                        item_name=item.name,
                        current_stock=current_stock,
                        vehicle_types=', '.join(sorted(vehicle_types))
                    ),
                    priority='high',
                    action_required=True,
                    action_url=f'/inventory/items/{item.id}/reorder/',
                    action_text=_('إعادة طلب'),
                    related_object_type='item',
                    related_object_id=str(item.id),
                    tenant_id=item.tenant_id,
                    metadata={
                        'item_id': str(item.id),
                        'current_stock': current_stock,
                        'reorder_level': reorder_level,
                        'vehicle_types': list(vehicle_types),
                        'alert_type': 'low_stock_vehicle_specific'
                    }
                )

        logger.info(f"Checked low stock for vehicle-specific item {item.id}")

    except Exception as e:
        logger.error(f"Error checking low stock for vehicle-specific parts: {e}")


def create_maintenance_due_notifications():
    """
    Periodic task to create maintenance due notifications based on vehicle age/mileage
    This should be called by a scheduled task (e.g., daily cron job)
    """
    try:
        from setup.models import Vehicle
        from datetime import datetime, timedelta

        # Get vehicles that might need maintenance
        vehicles = Vehicle.objects.filter(is_active=True)

        for vehicle in vehicles:
            # Calculate if maintenance is due based on age
            if vehicle.year:
                vehicle_age = datetime.now().year - vehicle.year

                # Different maintenance schedules based on age
                if vehicle_age >= 10:  # Older vehicles need more frequent maintenance
                    maintenance_interval_months = 3
                elif vehicle_age >= 5:  # Mid-age vehicles
                    maintenance_interval_months = 6
                else:  # Newer vehicles
                    maintenance_interval_months = 12

                # Check last maintenance (if we have work order history)
                from work_orders.models import WorkOrder
                last_maintenance = WorkOrder.objects.filter(
                    vehicle=vehicle,
                    status__in=['completed', 'closed'],
                    work_order_type__name__icontains='maintenance'
                ).order_by('-completed_at').first()

                maintenance_due = False
                if last_maintenance and last_maintenance.completed_at:
                    months_since_maintenance = (
                        timezone.now() - last_maintenance.completed_at
                    ).days / 30.44  # Average days per month

                    if months_since_maintenance >= maintenance_interval_months:
                        maintenance_due = True
                elif not last_maintenance:
                    # No maintenance history - recommend initial maintenance
                    maintenance_due = True

                if maintenance_due:
                    # Find vehicle owner or primary contact
                    owner = getattr(vehicle, 'owner', None) or vehicle.created_by

                    if owner:
                        NotificationService.create_notification(
                            recipient=owner,
                            notification_type_code='maintenance_due_reminder',
                            title=_('تذكير صيانة دورية مستحقة'),
                            message=_(
                                'المركبة {vehicle_make} {vehicle_model} {vehicle_year} تحتاج لصيانة دورية. '
                                'آخر صيانة كانت منذ {months_ago} شهر. يرجى حجز موعد صيانة.'
                            ).format(
                                vehicle_make=vehicle.make,
                                vehicle_model=vehicle.model,
                                vehicle_year=vehicle.year,
                                months_ago=int(months_since_maintenance) if last_maintenance else _('غير محدد')
                            ),
                            priority='medium',
                            action_required=True,
                            action_url=f'/vehicles/{vehicle.id}/schedule-maintenance/',
                            action_text=_('حجز صيانة'),
                            related_object_type='vehicle',
                            related_object_id=str(vehicle.id),
                            tenant_id=vehicle.tenant_id,
                            metadata={
                                'vehicle_id': str(vehicle.id),
                                'vehicle_age': vehicle_age,
                                'months_since_maintenance': int(months_since_maintenance) if last_maintenance else None,
                                'maintenance_type': 'periodic_due'
                            }
                        )

        logger.info("Created maintenance due notifications for eligible vehicles")

    except Exception as e:
        logger.error(f"Error creating maintenance due notifications: {e}")
