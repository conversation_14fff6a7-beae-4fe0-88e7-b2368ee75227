"""
Car Model Specific Notification Services
Advanced services for vehicle compatibility and maintenance notifications
"""

import logging
from django.utils.translation import gettext as _
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.db import models
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from .services import NotificationService

logger = logging.getLogger(__name__)


class CarModelNotificationService:
    """Service for handling car model specific notifications"""
    
    @staticmethod
    def check_work_order_compatibility(work_order):
        """
        Comprehensive compatibility check for work order items against vehicle
        
        Args:
            work_order: WorkOrder instance
            
        Returns:
            dict: Compatibility report with issues and recommendations
        """
        try:
            if not work_order.vehicle:
                return {'compatible': True, 'issues': [], 'recommendations': []}
            
            vehicle = work_order.vehicle
            issues = []
            recommendations = []
            
            # Check each work order material
            for wo_material in work_order.materials.all():
                item = wo_material.item
                
                # Check if item is compatible with vehicle using VehicleCompatibility
                is_compatible = item.vehicle_compatibilities.filter(
                    make=vehicle.make,
                    model=vehicle.model
                ).exists()

                # If year is specified, check year range
                if vehicle.year and is_compatible:
                    is_compatible = item.vehicle_compatibilities.filter(
                        make=vehicle.make,
                        model=vehicle.model,
                        year_from__lte=vehicle.year,
                        year_to__gte=vehicle.year
                    ).exists() or item.vehicle_compatibilities.filter(
                        make=vehicle.make,
                        model=vehicle.model,
                        year_from__lte=vehicle.year,
                        year_to__isnull=True
                    ).exists()

                if not is_compatible:
                    issues.append({
                        'type': 'incompatible_part',
                        'item': {
                            'id': str(item.id),
                            'name': item.name,
                            'sku': item.sku,
                            'category': item.get_category_display() if item.category else None
                        },
                        'message': _(
                            'القطعة "{item_name}" غير متوافقة مع {vehicle_make} {vehicle_model} {vehicle_year}'
                        ).format(
                            item_name=item.name,
                            vehicle_make=vehicle.make,
                            vehicle_model=vehicle.model,
                            vehicle_year=vehicle.year or _('غير محدد')
                        )
                    })
                    
                    # Find alternative compatible parts
                    alternatives = CarModelNotificationService.find_alternative_parts(
                        item, vehicle
                    )
                    
                    if alternatives:
                        recommendations.append({
                            'type': 'alternative_parts',
                            'original_item': {
                                'id': str(item.id),
                                'name': item.name,
                                'sku': item.sku,
                                'category': item.get_category_display() if item.category else None
                            },
                            'alternatives': alternatives,
                            'message': _(
                                'تم العثور على {count} قطعة بديلة متوافقة مع المركبة'
                            ).format(count=len(alternatives))
                        })
            
            # Check for missing recommended parts
            missing_parts = CarModelNotificationService.get_recommended_parts_for_operation(
                work_order.work_order_type, vehicle
            )
            
            if missing_parts:
                recommendations.append({
                    'type': 'recommended_parts',
                    'parts': missing_parts,
                    'message': _(
                        'يُنصح بإضافة {count} قطعة إضافية لهذا النوع من الصيانة'
                    ).format(count=len(missing_parts))
                })
            
            return {
                'compatible': len(issues) == 0,
                'issues': issues,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Error checking work order compatibility: {e}")
            return {'compatible': True, 'issues': [], 'recommendations': []}
    
    @staticmethod
    def find_alternative_parts(original_item, vehicle):
        """
        Find alternative parts that are compatible with the vehicle
        
        Args:
            original_item: Item instance that's incompatible
            vehicle: Vehicle instance
            
        Returns:
            list: List of compatible alternative items
        """
        try:
            from inventory.models import Item
            
            # Find items in same category that are compatible with vehicle
            alternatives = Item.objects.filter(
                classification=original_item.classification,
                tenant_id=original_item.tenant_id
            ).exclude(
                id=original_item.id
            )
            
            # Filter by vehicle compatibility
            compatible_alternatives = []
            for item in alternatives:
                # Check if item is compatible with vehicle using VehicleCompatibility
                is_compatible = item.vehicle_compatibilities.filter(
                    make=vehicle.make,
                    model=vehicle.model
                ).exists()

                # If year is specified, check year range
                if vehicle.year and is_compatible:
                    is_compatible = item.vehicle_compatibilities.filter(
                        make=vehicle.make,
                        model=vehicle.model,
                        year_from__lte=vehicle.year,
                        year_to__gte=vehicle.year
                    ).exists() or item.vehicle_compatibilities.filter(
                        make=vehicle.make,
                        model=vehicle.model,
                        year_from__lte=vehicle.year,
                        year_to__isnull=True
                    ).exists()

                if is_compatible:
                    compatible_alternatives.append({
                        'id': str(item.id),
                        'name': item.name,
                        'sku': item.sku,
                        'category': item.get_category_display() if item.category else None,
                        'unit_price': float(item.unit_price) if item.unit_price else 0,
                        'current_stock': item.current_stock
                    })

            return compatible_alternatives[:5]  # Return top 5 alternatives
            
        except Exception as e:
            logger.error(f"Error finding alternative parts: {e}")
            return []
    
    @staticmethod
    def get_recommended_parts_for_operation(operation_type, vehicle):
        """
        Get recommended parts for a specific operation type and vehicle
        
        Args:
            operation_type: OperationType instance
            vehicle: Vehicle instance
            
        Returns:
            list: List of recommended items
        """
        try:
            from inventory.models import OperationCompatibility
            
            if not operation_type:
                return []
            
            # Get operation compatibilities for this operation and vehicle
            # Since Vehicle.make is CharField but VehicleOperationCompatibility.vehicle_make is ForeignKey,
            # we need to find compatible operations differently
            from setup.models import VehicleMake, VehicleModel

            try:
                vehicle_make_obj = VehicleMake.objects.get(name=vehicle.make)
                vehicle_model_obj = None
                if vehicle.model:
                    try:
                        vehicle_model_obj = VehicleModel.objects.get(make=vehicle_make_obj, name=vehicle.model)
                    except VehicleModel.DoesNotExist:
                        pass

                # Filter operation compatibilities
                compatible_ops = OperationCompatibility.objects.filter(
                    operation_type=operation_type,
                    vehicle_compatibilities__vehicle_make=vehicle_make_obj
                )

                if vehicle_model_obj:
                    compatible_ops = compatible_ops.filter(
                        models.Q(vehicle_compatibilities__vehicle_model=vehicle_model_obj) |
                        models.Q(vehicle_compatibilities__vehicle_model__isnull=True)
                    )

                if vehicle.year:
                    compatible_ops = compatible_ops.filter(
                        models.Q(vehicle_compatibilities__year_from__isnull=True) |
                        models.Q(vehicle_compatibilities__year_from__lte=vehicle.year)
                    ).filter(
                        models.Q(vehicle_compatibilities__year_to__isnull=True) |
                        models.Q(vehicle_compatibilities__year_to__gte=vehicle.year)
                    )

            except VehicleMake.DoesNotExist:
                compatible_ops = OperationCompatibility.objects.none()
            
            recommended_items = []
            for op_compat in compatible_ops:
                if op_compat.item:
                    # Check if item is compatible with vehicle using VehicleCompatibility
                    is_compatible = op_compat.item.vehicle_compatibilities.filter(
                        make=vehicle.make,
                        model=vehicle.model
                    ).exists()

                    # If year is specified, check year range
                    if vehicle.year and is_compatible:
                        is_compatible = op_compat.item.vehicle_compatibilities.filter(
                            make=vehicle.make,
                            model=vehicle.model,
                            year_from__lte=vehicle.year,
                            year_to__gte=vehicle.year
                        ).exists() or op_compat.item.vehicle_compatibilities.filter(
                            make=vehicle.make,
                            model=vehicle.model,
                            year_from__lte=vehicle.year,
                            year_to__isnull=True
                        ).exists()

                    if is_compatible:
                        recommended_items.append({
                            'id': str(op_compat.item.id),
                            'name': op_compat.item.name,
                            'sku': op_compat.item.sku,
                            'category': op_compat.item.get_category_display() if op_compat.item.category else None,
                            'unit_price': float(op_compat.item.unit_price) if op_compat.item.unit_price else 0,
                            'current_stock': op_compat.item.current_stock
                        })

            return recommended_items
            
        except Exception as e:
            logger.error(f"Error getting recommended parts: {e}")
            return []
    
    @staticmethod
    def create_vehicle_recall_notification(vehicle_make, vehicle_model, year_from, year_to, recall_info):
        """
        Create recall notifications for specific vehicle models
        
        Args:
            vehicle_make: str
            vehicle_model: str  
            year_from: int
            year_to: int (optional)
            recall_info: dict with recall details
        """
        try:
            from setup.models import Vehicle
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # Find affected vehicles
            vehicles_query = Vehicle.objects.filter(
                make__iexact=vehicle_make,
                model__iexact=vehicle_model,
                year__gte=year_from
            )
            
            if year_to:
                vehicles_query = vehicles_query.filter(year__lte=year_to)
            
            affected_vehicles = vehicles_query.distinct()
            
            # Notify vehicle owners and service centers
            for vehicle in affected_vehicles:
                # Notify vehicle owner
                owner = getattr(vehicle, 'owner', None) or vehicle.created_by
                if owner:
                    NotificationService.create_notification(
                        recipient=owner,
                        notification_type_code='vehicle_recall_alert',
                        title=_('تنبيه استدعاء مركبة'),
                        message=_(
                            'تم إصدار استدعاء للمركبة {vehicle_make} {vehicle_model} {vehicle_year}. '
                            'السبب: {recall_reason}. يرجى الاتصال بمركز الخدمة فوراً.'
                        ).format(
                            vehicle_make=vehicle.make,
                            vehicle_model=vehicle.model,
                            vehicle_year=vehicle.year,
                            recall_reason=recall_info.get('reason', _('غير محدد'))
                        ),
                        priority='critical',
                        action_required=True,
                        action_url=f'/vehicles/{vehicle.id}/recall/',
                        action_text=_('عرض تفاصيل الاستدعاء'),
                        related_object_type='vehicle',
                        related_object_id=str(vehicle.id),
                        tenant_id=vehicle.tenant_id,
                        metadata={
                            'vehicle_id': str(vehicle.id),
                            'recall_info': recall_info,
                            'notification_type': 'recall_alert'
                        }
                    )
                
                # Notify service center
                if vehicle.service_center:
                    service_staff = User.objects.filter(
                        groups__name__in=['service_advisor', 'service_center_manager'],
                        userprofile__service_center=vehicle.service_center
                    )
                    
                    for staff in service_staff:
                        NotificationService.create_notification(
                            recipient=staff,
                            notification_type_code='service_center_recall_alert',
                            title=_('تنبيه استدعاء - مركز الخدمة'),
                            message=_(
                                'مركبة عميل تحتاج لاستدعاء: {vehicle_make} {vehicle_model} {vehicle_year}. '
                                'يرجى الاتصال بالعميل وجدولة موعد للإصلاح.'
                            ).format(
                                vehicle_make=vehicle.make,
                                vehicle_model=vehicle.model,
                                vehicle_year=vehicle.year
                            ),
                            priority='high',
                            action_required=True,
                            action_url=f'/vehicles/{vehicle.id}/',
                            action_text=_('عرض المركبة'),
                            tenant_id=vehicle.tenant_id,
                            metadata={
                                'vehicle_id': str(vehicle.id),
                                'recall_info': recall_info,
                                'notification_type': 'service_recall_alert'
                            }
                        )
            
            logger.info(f"Created recall notifications for {affected_vehicles.count()} vehicles")
            
        except Exception as e:
            logger.error(f"Error creating vehicle recall notifications: {e}")
    
    @staticmethod
    def analyze_vehicle_maintenance_patterns(vehicle):
        """
        Analyze maintenance patterns for a vehicle and suggest optimizations
        
        Args:
            vehicle: Vehicle instance
            
        Returns:
            dict: Analysis results with recommendations
        """
        try:
            from work_orders.models import WorkOrder
            
            # Get maintenance history
            maintenance_orders = WorkOrder.objects.filter(
                vehicle=vehicle,
                status='completed'
            ).order_by('-updated_at')
            
            if not maintenance_orders.exists():
                return {
                    'has_history': False,
                    'recommendations': [_('لا يوجد تاريخ صيانة لهذه المركبة')]
                }
            
            # Analyze patterns
            analysis = {
                'has_history': True,
                'total_services': maintenance_orders.count(),
                'avg_interval_days': 0,
                'common_issues': [],
                'cost_trend': 'stable',
                'recommendations': []
            }
            
            # Calculate average service interval
            if maintenance_orders.count() > 1:
                intervals = []
                prev_date = None
                for order in maintenance_orders:
                    if prev_date and order.updated_at:
                        interval = (prev_date - order.updated_at).days
                        intervals.append(interval)
                    prev_date = order.updated_at
                
                if intervals:
                    analysis['avg_interval_days'] = sum(intervals) / len(intervals)
            
            # Find common issues
            from django.db.models import Count
            common_types = maintenance_orders.values('work_order_type__name').annotate(
                count=Count('id')
            ).order_by('-count')[:3]
            
            analysis['common_issues'] = [
                item['work_order_type__name'] for item in common_types
                if item['work_order_type__name']
            ]
            
            # Generate recommendations
            if analysis['avg_interval_days'] > 365:
                analysis['recommendations'].append(
                    _('فترات الصيانة طويلة جداً. يُنصح بصيانة أكثر تكراراً.')
                )
            elif analysis['avg_interval_days'] < 90:
                analysis['recommendations'].append(
                    _('فترات الصيانة قصيرة. قد تحتاج المركبة لفحص شامل.')
                )
            
            if len(analysis['common_issues']) > 0:
                analysis['recommendations'].append(
                    _('المشاكل المتكررة: {issues}. يُنصح بفحص وقائي.').format(
                        issues=', '.join(analysis['common_issues'])
                    )
                )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing vehicle maintenance patterns: {e}")
            return {'has_history': False, 'recommendations': [_('خطأ في تحليل البيانات')]}
