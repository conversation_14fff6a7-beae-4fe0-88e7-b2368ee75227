"""
Car Model Compatibility API Views
API endpoints for vehicle compatibility checks and notifications
"""

import logging
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.utils.translation import gettext as _
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import json

from .car_model_services import CarModelNotificationService
from .services import NotificationService

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_work_order_compatibility(request):
    """
    Check compatibility of work order items with assigned vehicle
    
    POST /api/notifications/check-work-order-compatibility/
    {
        "work_order_id": "uuid"
    }
    """
    try:
        data = request.data
        work_order_id = data.get('work_order_id')
        
        if not work_order_id:
            return Response(
                {'error': _('معرف أمر العمل مطلوب')},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get work order
        from work_orders.models import WorkOrder
        work_order = get_object_or_404(WorkOrder, id=work_order_id)
        
        # Check compatibility
        compatibility_report = CarModelNotificationService.check_work_order_compatibility(
            work_order
        )
        
        # If there are compatibility issues, create notifications
        if not compatibility_report['compatible']:
            for issue in compatibility_report['issues']:
                if issue['type'] == 'incompatible_part':
                    NotificationService.create_notification(
                        recipient=work_order.assigned_technician,
                        notification_type_code='vehicle_compatibility_report',
                        title=_('تقرير توافق المركبة'),
                        message=issue['message'],
                        priority='high',
                        action_required=True,
                        action_url=f'/work-orders/{work_order.id}/',
                        action_text=_('مراجعة أمر العمل'),
                        related_object_type='work_order',
                        related_object_id=str(work_order.id),
                        tenant_id=work_order.tenant_id,
                        metadata={
                            'compatibility_report': compatibility_report,
                            'work_order_id': str(work_order.id)
                        }
                    )
        
        return Response({
            'success': True,
            'compatibility_report': compatibility_report,
            'message': _('تم فحص التوافق بنجاح')
        })
        
    except Exception as e:
        logger.error(f"Error checking work order compatibility: {e}")
        return Response(
            {'error': _('خطأ في فحص التوافق')},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_vehicle_compatible_parts(request, vehicle_id):
    """
    Get parts compatible with a specific vehicle
    
    GET /api/notifications/vehicle/{vehicle_id}/compatible-parts/
    """
    try:
        from setup.models import Vehicle
        from inventory.models import Item
        
        vehicle = get_object_or_404(Vehicle, id=vehicle_id)
        
        # Get compatible items - use vehicle's tenant_id for consistency
        vehicle_tenant_id = getattr(vehicle, 'tenant_id', None)

        if vehicle_tenant_id:
            compatible_items = Item.objects.filter(tenant_id=vehicle_tenant_id)
        else:
            # Fallback to all items if no tenant_id
            compatible_items = Item.objects.all()
        
        # Filter by compatibility
        compatible_parts = []

        for item in compatible_items:
            # Check if item is compatible with vehicle using VehicleCompatibility
            compatibilities = item.vehicle_compatibilities.filter(
                make=vehicle.make,
                model=vehicle.model
            )

            is_compatible = compatibilities.exists()

            # If year is specified, check year range
            if vehicle.year and is_compatible:
                year_compatible = item.vehicle_compatibilities.filter(
                    make=vehicle.make,
                    model=vehicle.model,
                    year_from__lte=vehicle.year,
                    year_to__gte=vehicle.year
                ).exists() or item.vehicle_compatibilities.filter(
                    make=vehicle.make,
                    model=vehicle.model,
                    year_from__lte=vehicle.year,
                    year_to__isnull=True
                ).exists()
                is_compatible = year_compatible

            if is_compatible:
                compatible_parts.append({
                    'id': str(item.id),
                    'name': item.name,
                    'sku': item.sku,
                    'category': item.get_category_display() if item.category else None,
                    'current_stock': item.current_stock,
                    'unit_price': float(item.unit_price) if item.unit_price else 0
                })
        
        return Response({
            'success': True,
            'vehicle': {
                'id': str(vehicle.id),
                'make': vehicle.make,
                'model': vehicle.model,
                'year': vehicle.year
            },
            'compatible_parts': compatible_parts,
            'total_count': len(compatible_parts)
        })
        
    except Exception as e:
        logger.error(f"Error getting vehicle compatible parts: {e}")
        return Response(
            {'error': _('خطأ في جلب القطع المتوافقة')},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_vehicle_recall_notification(request):
    """
    Create recall notifications for specific vehicle models
    
    POST /api/notifications/create-vehicle-recall/
    {
        "vehicle_make": "Toyota",
        "vehicle_model": "Camry", 
        "year_from": 2018,
        "year_to": 2020,
        "recall_info": {
            "reason": "Brake system issue",
            "severity": "high",
            "description": "...",
            "reference_number": "RC-2024-001"
        }
    }
    """
    try:
        data = request.data
        
        required_fields = ['vehicle_make', 'vehicle_model', 'year_from', 'recall_info']
        for field in required_fields:
            if field not in data:
                return Response(
                    {'error': _(f'الحقل {field} مطلوب')},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Create recall notifications
        CarModelNotificationService.create_vehicle_recall_notification(
            vehicle_make=data['vehicle_make'],
            vehicle_model=data['vehicle_model'],
            year_from=data['year_from'],
            year_to=data.get('year_to'),
            recall_info=data['recall_info']
        )
        
        return Response({
            'success': True,
            'message': _('تم إنشاء إشعارات الاستدعاء بنجاح')
        })
        
    except Exception as e:
        logger.error(f"Error creating vehicle recall notifications: {e}")
        return Response(
            {'error': _('خطأ في إنشاء إشعارات الاستدعاء')},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analyze_vehicle_maintenance(request, vehicle_id):
    """
    Analyze maintenance patterns for a vehicle
    
    GET /api/notifications/vehicle/{vehicle_id}/maintenance-analysis/
    """
    try:
        from setup.models import Vehicle
        
        vehicle = get_object_or_404(Vehicle, id=vehicle_id)
        
        # Analyze maintenance patterns
        analysis = CarModelNotificationService.analyze_vehicle_maintenance_patterns(vehicle)
        
        # Create notification if analysis reveals important insights
        if analysis['has_history'] and analysis['recommendations']:
            NotificationService.create_notification(
                recipient=vehicle.created_by,
                notification_type_code='maintenance_pattern_analysis',
                title=_('تحليل أنماط الصيانة'),
                message=_(
                    'تم تحليل أنماط صيانة المركبة {vehicle_make} {vehicle_model}. '
                    'عدد التوصيات: {recommendations_count}'
                ).format(
                    vehicle_make=vehicle.make,
                    vehicle_model=vehicle.model,
                    recommendations_count=len(analysis['recommendations'])
                ),
                priority='medium',
                action_required=False,
                action_url=f'/vehicles/{vehicle.id}/maintenance-analysis/',
                action_text=_('عرض التحليل'),
                related_object_type='vehicle',
                related_object_id=str(vehicle.id),
                tenant_id=vehicle.tenant_id,
                metadata={
                    'vehicle_id': str(vehicle.id),
                    'analysis': analysis
                }
            )
        
        return Response({
            'success': True,
            'vehicle': {
                'id': str(vehicle.id),
                'make': vehicle.make,
                'model': vehicle.model,
                'year': vehicle.year
            },
            'analysis': analysis
        })
        
    except Exception as e:
        logger.error(f"Error analyzing vehicle maintenance: {e}")
        return Response(
            {'error': _('خطأ في تحليل الصيانة')},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_maintenance_due_check(request):
    """
    Manually trigger maintenance due notifications check
    
    POST /api/notifications/trigger-maintenance-check/
    """
    try:
        from notifications.car_model_signals import create_maintenance_due_notifications
        
        # Run the maintenance due check
        create_maintenance_due_notifications()
        
        return Response({
            'success': True,
            'message': _('تم تشغيل فحص الصيانة المستحقة بنجاح')
        })
        
    except Exception as e:
        logger.error(f"Error triggering maintenance due check: {e}")
        return Response(
            {'error': _('خطأ في تشغيل فحص الصيانة')},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
