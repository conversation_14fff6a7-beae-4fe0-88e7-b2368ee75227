"""
Management command to initialize car model specific notification types
"""

from django.core.management.base import BaseCommand
from django.utils.translation import gettext as _
from notifications.models import NotificationType


class Command(BaseCommand):
    help = 'Initialize car model specific notification types'

    def handle(self, *args, **options):
        """Create car model specific notification types"""
        
        car_model_notifications = [
            {
                'name': _('تحذير قطعة غير متوافقة'),
                'code': 'incompatible_part_alert',
                'type': 'incompatible_part_alert',
                'description': _('تحذير عند إضافة قطعة غير متوافقة مع المركبة في أمر العمل'),
                'icon': 'fas fa-exclamation-triangle',
                'color': 'red'
            },
            {
                'name': _('تنبيه مدير - توافق القطع'),
                'code': 'manager_compatibility_alert',
                'type': 'manager_compatibility_alert',
                'description': _('تنبيه للمدير عند وجود مشاكل توافق القطع مع المركبات'),
                'icon': 'fas fa-user-shield',
                'color': 'orange'
            },
            {
                'name': _('جدولة صيانة المركبة'),
                'code': 'vehicle_maintenance_schedule',
                'type': 'vehicle_maintenance_schedule',
                'description': _('إشعار بتوفر جدولة صيانة للمركبة الجديدة'),
                'icon': 'fas fa-calendar-check',
                'color': 'blue'
            },
            {
                'name': _('قطعة جديدة متوافقة'),
                'code': 'new_compatible_part',
                'type': 'new_compatible_part',
                'description': _('إشعار بتوفر قطعة جديدة متوافقة مع مركبات معينة'),
                'icon': 'fas fa-plus-circle',
                'color': 'green'
            },
            {
                'name': _('نفاد مخزون قطعة مركبة'),
                'code': 'low_stock_vehicle_part',
                'type': 'low_stock_vehicle_part',
                'description': _('تحذير نفاد مخزون قطعة خاصة بمركبات معينة'),
                'icon': 'fas fa-box-open',
                'color': 'red'
            },
            {
                'name': _('تذكير صيانة مستحقة'),
                'code': 'maintenance_due_reminder',
                'type': 'maintenance_due_reminder',
                'description': _('تذكير بموعد الصيانة الدورية للمركبة'),
                'icon': 'fas fa-wrench',
                'color': 'yellow'
            },
            {
                'name': _('تنبيه استدعاء مركبة'),
                'code': 'vehicle_recall_alert',
                'type': 'vehicle_recall_alert',
                'description': _('تنبيه بوجود استدعاء للمركبة من الشركة المصنعة'),
                'icon': 'fas fa-exclamation-circle',
                'color': 'red'
            },
            {
                'name': _('تنبيه استدعاء - مركز خدمة'),
                'code': 'service_center_recall_alert',
                'type': 'service_center_recall_alert',
                'description': _('تنبيه لمركز الخدمة بوجود مركبة عميل تحتاج لاستدعاء'),
                'icon': 'fas fa-building',
                'color': 'orange'
            },
            {
                'name': _('تقرير توافق المركبة'),
                'code': 'vehicle_compatibility_report',
                'type': 'vehicle_compatibility_report',
                'description': _('تقرير شامل عن توافق القطع مع المركبة'),
                'icon': 'fas fa-file-alt',
                'color': 'blue'
            },
            {
                'name': _('تحليل أنماط الصيانة'),
                'code': 'maintenance_pattern_analysis',
                'type': 'maintenance_pattern_analysis',
                'description': _('تحليل أنماط الصيانة وتوصيات التحسين'),
                'icon': 'fas fa-chart-line',
                'color': 'purple'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for notification_data in car_model_notifications:
            notification_type, created = NotificationType.objects.get_or_create(
                code=notification_data['code'],
                defaults=notification_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created notification type: {notification_type.name}'
                    )
                )
            else:
                # Update existing notification type
                for key, value in notification_data.items():
                    if key != 'code':
                        setattr(notification_type, key, value)
                notification_type.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(
                        f'Updated notification type: {notification_type.name}'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCar model notification types initialized successfully!'
            )
        )
        self.stdout.write(f'Created: {created_count}')
        self.stdout.write(f'Updated: {updated_count}')
        self.stdout.write(f'Total: {len(car_model_notifications)}')
