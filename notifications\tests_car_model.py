"""
Tests for Car Model Specific Notifications
"""

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock

from setup.models import Vehicle, VehicleMake, VehicleModel
from inventory.models import Item, ItemClassification, VehicleCompatibility
from work_orders.models import WorkOrder, WorkOrderMaterial, WorkOrderType
from notifications.models import Notification, NotificationType
from notifications.car_model_services import CarModelNotificationService
import uuid

User = get_user_model()


class CarModelNotificationTestCase(TestCase):
    """Test case for car model specific notifications"""
    
    def setUp(self):
        """Set up test data"""
        # Create tenant ID
        self.tenant_id = uuid.uuid4()
        
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create user profile with tenant
        from setup.models import User<PERSON>ro<PERSON>le, User<PERSON><PERSON> as SetupUserRole
        from user_roles.models import Role
        from django.contrib.auth.models import Group

        # Create a test role
        group = Group.objects.create(name="test_role_group")
        self.role = Role.objects.create(
            name="Test Role",
            code="test_role",
            role_type="service_advisor",
            description="Test role for notifications",
            group=group
        )

        # Create setup user role
        self.setup_role = SetupUserRole.objects.create(
            name="Test Setup Role",
            level="service_center"
        )

        self.user_profile = UserProfile.objects.create(
            user=self.user,
            role=self.setup_role,
            tenant_id=self.tenant_id
        )
        
        # Create vehicle makes and models
        self.vehicle_make, _ = VehicleMake.objects.get_or_create(
            name="Toyota",
            defaults={'tenant_id': self.tenant_id}
        )

        self.vehicle_model, _ = VehicleModel.objects.get_or_create(
            make=self.vehicle_make,
            name="Camry",
            defaults={'tenant_id': self.tenant_id}
        )

        # Create vehicle
        self.vehicle = Vehicle.objects.create(
            make="Toyota",
            model="Camry",
            year=2020,
            vin="1234567890",
            tenant_id=self.tenant_id
        )
        
        # Create category
        self.category = ItemClassification.objects.create(
            name="Engine Parts",
            code="ENG",
            tenant_id=self.tenant_id
        )
        
        # Create compatible item
        self.compatible_item = Item.objects.create(
            name="Compatible Oil Filter",
            sku="COF001",
            classification=self.category,
            unit_price=25.00,
            quantity=10,
            tenant_id=self.tenant_id
        )
        
        # Create vehicle compatibility
        VehicleCompatibility.objects.create(
            item=self.compatible_item,
            make="Toyota",
            model="Camry",
            year_from=2018,
            year_to=2022,
            tenant_id=self.tenant_id
        )

        # Create incompatible item
        self.incompatible_item = Item.objects.create(
            name="Incompatible Air Filter",
            sku="IAF001",
            classification=self.category,
            unit_price=30.00,
            quantity=5,
            tenant_id=self.tenant_id
        )

        # Create vehicle compatibility for different vehicle
        VehicleCompatibility.objects.create(
            item=self.incompatible_item,
            make="Honda",
            model="Civic",
            year_from=2015,
            year_to=2020,
            tenant_id=self.tenant_id
        )
        
        # Create operation type
        self.operation_type = WorkOrderType.objects.create(
            name="Oil Change",
            tenant_id=self.tenant_id
        )
        
        # Create work order
        self.work_order = WorkOrder.objects.create(
            work_order_number="WO001",
            vehicle=self.vehicle,
            work_order_type=self.operation_type,
            status="planned",
            description="Test work order",
            tenant_id=self.tenant_id
        )
        
        # Create notification types
        self.notification_types = {}
        notification_type_codes = [
            'incompatible_part_alert',
            'vehicle_maintenance_schedule',
            'new_compatible_part',
            'low_stock_vehicle_part',
            'maintenance_due_reminder'
        ]
        
        for code in notification_type_codes:
            nt = NotificationType.objects.create(
                name=f"Test {code}",
                code=code,
                type=code
            )
            self.notification_types[code] = nt
        
        self.client = Client()
        self.client.force_login(self.user)
    
    def test_incompatible_part_notification(self):
        """Test notification creation for incompatible parts"""
        # Add incompatible item to work order
        work_order_material = WorkOrderMaterial.objects.create(
            tenant_id=self.tenant_id,
            work_order=self.work_order,
            item=self.incompatible_item,
            quantity=1,
            unit_of_measure="pcs"
        )
        
        # Check if notification was created
        notifications = Notification.objects.filter(
            recipient=self.user,
            notification_type__code='incompatible_part_alert'
        )
        
        self.assertTrue(notifications.exists())
        notification = notifications.first()
        self.assertIn("غير متوافقة", notification.title)
        self.assertIn("Toyota Camry", notification.message)
        self.assertIn("Incompatible Air Filter", notification.message)
    
    def test_compatible_part_no_notification(self):
        """Test that compatible parts don't trigger incompatibility notifications"""
        # Add compatible item to work order
        work_order_material = WorkOrderMaterial.objects.create(
            work_order=self.work_order,
            item=self.compatible_item,
            quantity=1,
            unit_of_measure="pcs"
        )
        
        # Check that no incompatibility notification was created
        notifications = Notification.objects.filter(
            recipient=self.user,
            notification_type__code='incompatible_part_alert'
        )
        
        self.assertFalse(notifications.exists())
    
    def test_vehicle_maintenance_schedule_notification(self):
        """Test maintenance schedule notification for new vehicle"""
        # Create a new vehicle (this should trigger the signal)
        new_vehicle = Vehicle.objects.create(
            make="Honda",
            model="Civic",
            year=2021,
            vin="9876543210",
            tenant_id=self.tenant_id
        )
        
        # Check if maintenance schedule notification was created
        notifications = Notification.objects.filter(
            recipient=self.user,
            notification_type__code='vehicle_maintenance_schedule'
        )
        
        # Note: This might not trigger in tests without proper signal setup
        # In real application, the signal would be triggered
    
    def test_compatibility_check_service(self):
        """Test the compatibility check service"""
        # Add both compatible and incompatible items
        WorkOrderMaterial.objects.create(
            work_order=self.work_order,
            item=self.compatible_item,
            quantity=1,
            unit_of_measure="pcs"
        )

        WorkOrderMaterial.objects.create(
            work_order=self.work_order,
            item=self.incompatible_item,
            quantity=1,
            unit_of_measure="pcs"
        )
        
        # Check compatibility
        report = CarModelNotificationService.check_work_order_compatibility(
            self.work_order
        )
        
        self.assertFalse(report['compatible'])
        self.assertEqual(len(report['issues']), 1)
        self.assertEqual(report['issues'][0]['type'], 'incompatible_part')
    
    def test_find_alternative_parts(self):
        """Test finding alternative compatible parts"""
        alternatives = CarModelNotificationService.find_alternative_parts(
            self.incompatible_item, self.vehicle
        )
        
        # Should find the compatible item as alternative
        self.assertEqual(len(alternatives), 1)
        self.assertEqual(alternatives[0]['id'], str(self.compatible_item.id))
    
    def test_api_check_work_order_compatibility(self):
        """Test the API endpoint for checking work order compatibility"""
        # Add incompatible item
        WorkOrderMaterial.objects.create(
            work_order=self.work_order,
            item=self.incompatible_item,
            quantity=1,
            unit_of_measure="pcs"
        )
        
        url = reverse('notifications:api_check_compatibility')
        response = self.client.post(url, {
            'work_order_id': str(self.work_order.id)
        }, content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertFalse(data['compatibility_report']['compatible'])
    
    def test_api_get_vehicle_compatible_parts(self):
        """Test the API endpoint for getting vehicle compatible parts"""
        url = reverse('notifications:api_vehicle_compatible_parts', 
                     kwargs={'vehicle_id': self.vehicle.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['compatible_parts']), 1)
        self.assertEqual(data['compatible_parts'][0]['name'], "Compatible Oil Filter")
    
    def test_maintenance_pattern_analysis(self):
        """Test vehicle maintenance pattern analysis"""
        analysis = CarModelNotificationService.analyze_vehicle_maintenance_patterns(
            self.vehicle
        )
        
        # New vehicle should have no history
        self.assertFalse(analysis['has_history'])
        self.assertIn('لا يوجد تاريخ صيانة', analysis['recommendations'][0])
    
    @patch('notifications.car_model_signals.create_maintenance_due_notifications')
    def test_trigger_maintenance_check_api(self, mock_maintenance_check):
        """Test the API endpoint for triggering maintenance check"""
        url = reverse('notifications:api_trigger_maintenance_check')
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        mock_maintenance_check.assert_called_once()
    
    def tearDown(self):
        """Clean up test data"""
        # Clean up is handled automatically by Django test framework
        pass
